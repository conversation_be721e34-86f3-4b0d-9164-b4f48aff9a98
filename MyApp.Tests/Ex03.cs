using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MyApp.Tests;

[TestClass]
public class Ex03
{
    [TestMethod]
    public void Test1()
    {
        int result = MethodLibrary.MaxAndMean(1, 2, 3, out global::System.Double mean);
        Assert.AreEqual(3, result);
        Assert.AreEqual(2, mean);
    }

    [TestMethod]
    public void Test2()
    {
        int result = MethodLibrary.MaxAndMean(4, 5, 6, out global::System.Double mean);
        Assert.AreEqual(6, result);
        Assert.AreEqual(5, mean);
    }

    [TestMethod]
    public void Test3()
    {
        int result = MethodLibrary.MaxAndMean(7, 8, 9, out global::System.Double mean);
        Assert.AreEqual(9, result);
        Assert.AreEqual(8, mean);
    }

    [TestMethod]
    public void Test4()
    {
        int result = MethodLibrary.MaxAndMean(10, 11, 12, out global::System.Double mean);
        Assert.AreEqual(12, result);
        Assert.AreEqual(11, mean);
    }
}