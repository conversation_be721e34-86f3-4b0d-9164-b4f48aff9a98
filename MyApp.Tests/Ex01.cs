namespace MyApp.Tests;

[TestClass]
public class Ex01
{
    [TestMethod]
    public void Test1()
    {
        int result = MethodLibrary.GetPositionOf2Cir(10, 10, 0);
        Assert.AreEqual(0, result);
    }

    [TestMethod]
    public void Test2()
    {
        int result = MethodLibrary.GetPositionOf2Cir(15, 20, 0);
        Assert.AreEqual(1, result);
    }

    [TestMethod]
    public void Test3()
    {
        int result = MethodLibrary.GetPositionOf2Cir(30, 25, 0);
        Assert.AreEqual(2, result);
    }

    [TestMethod]
    public void Test4()
    {
        int result = MethodLibrary.GetPositionOf2Cir(45, 60, 6);
        Assert.AreEqual(3, result);
    }

    [TestMethod]
    public void Test5()
    {
        int result = MethodLibrary.GetPositionOf2Cir(30, 75, -12);
        Assert.AreEqual(-1, result);
    }
}
