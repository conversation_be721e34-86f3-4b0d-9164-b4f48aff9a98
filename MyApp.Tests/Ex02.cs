namespace MyApp.Tests;

[TestClass]
public class Ex02
{
    [TestMethod]
    public void Test1()
    {
        string result = MethodLibrary.GetResult(1, 2, 3);
        Assert.AreEqual("A", result);
    }

    [TestMethod]
    public void Test2()
    {
        string result = MethodLibrary.GetResult(3, 5, 7);
        Assert.AreEqual("B", result);
    }   

    [TestMethod]
    public void Test3()
    {
        string result = MethodLibrary.GetResult(4, 12, 3);
        Assert.AreEqual("C", result);
    }

    [TestMethod]
    public void Test4()
    {
        string result = MethodLibrary.GetResult(5, 11, 5);
        Assert.AreEqual("D", result);
    }
}