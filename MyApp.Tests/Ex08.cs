namespace MyApp.Tests;

[TestClass]
public class Ex08
{
    [TestMethod]
    public void Test1()
    {
        using var sw = new StringWriter();
        Console.SetOut(sw);
        QuadraticSolver.Solve(0, 2, 3);
        Assert.AreEqual("error", sw.ToString().Trim());
    }

    [TestMethod]
    public void Test2()
    {
        using var sw = new StringWriter();
        Console.SetOut(sw);
        QuadraticSolver.Solve(1, 1, 1);
        Assert.AreEqual("no results", sw.ToString().Trim());
    }

    [TestMethod]
    public void Test3()
    {
        using var sw = new StringWriter();
        Console.SetOut(sw);
        QuadraticSolver.Solve(1, 2, 1);
        Assert.AreEqual("x = -1", sw.ToString().Trim());
    }

    [TestMethod]
    public void Test4()
    {
        using var sw = new StringWriter();
        Console.SetOut(sw);
        QuadraticSolver.Solve(1, -4, 3);

        var output = sw.ToString().Trim().Split(Environment.NewLine);

        Assert.AreEqual("x1 = 1", output[0]);
        Assert.AreEqual("x2 = 3", output[1]);
    }
}