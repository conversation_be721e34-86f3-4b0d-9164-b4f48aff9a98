using Microsoft.VisualStudio.TestTools.UnitTesting;
using MyApp;

namespace MyApp.Tests;

[TestClass]
public class Ex08
{
    [TestMethod]
    public void Test1()
    {
        var sw = new StringWriter();
        var originalOut = Console.Out;
        try
        {
            Console.SetOut(sw);
            MethodLibrary.Solve(0, 2, 3);
            Assert.AreEqual("error", sw.ToString().Trim());
        }
        finally
        {
            Console.SetOut(originalOut);
            sw.Dispose();
        }
    }

    [TestMethod]
    public void Test2()
    {
        var sw = new StringWriter();
        var originalOut = Console.Out;
        try
        {
            Console.SetOut(sw);
            MethodLibrary.Solve(1, 1, 1);
            Assert.AreEqual("no results", sw.ToString().Trim());
        }
        finally
        {
            Console.SetOut(originalOut);
            sw.Dispose();
        }
    }

    [TestMethod]
    public void Test3()
    {
        var sw = new StringWriter();
        var originalOut = Console.Out;
        try
        {
            Console.SetOut(sw);
            MethodLibrary.Solve(1, 2, 1);
            Assert.AreEqual("x = -1", sw.ToString().Trim());
        }
        finally
        {
            Console.SetOut(originalOut);
            sw.Dispose();
        }
    }

    [TestMethod]
    public void Test4()
    {
        var sw = new StringWriter();
        var originalOut = Console.Out;
        try
        {
            Console.SetOut(sw);
            MethodLibrary.Solve(1, -4, 3);

            var output = sw.ToString().Trim().Split(Environment.NewLine);

            Assert.AreEqual("x1 = 1", output[0]);
            Assert.AreEqual("x2 = 3", output[1]);
        }
        finally
        {
            Console.SetOut(originalOut);
            sw.Dispose();
        }
    }
}