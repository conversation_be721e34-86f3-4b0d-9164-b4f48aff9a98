using Microsoft.VisualStudio.TestTools.UnitTesting;
using MyApp;

namespace MyApp.Tests;

[TestClass]
[DoNotParallelize]
public class Ex08
{
    [TestMethod]
    public void Test1()
    {
        var sw = new StringWriter();
        var originalOut = Console.Out;
        Console.SetOut(sw);

        MethodLibrary.Solve(0, 2, 3);

        var output = sw.ToString().Trim();
        Console.SetOut(originalOut);
        sw.Dispose();

        Assert.AreEqual("error", output);
    }

    [TestMethod]
    public void Test2()
    {
        var sw = new StringWriter();
        var originalOut = Console.Out;
        Console.SetOut(sw);

        MethodLibrary.Solve(1, 1, 1);

        var output = sw.ToString().Trim();
        Console.SetOut(originalOut);
        sw.Dispose();

        Assert.AreEqual("no results", output);
    }

    [TestMethod]
    public void Test3()
    {
        var sw = new StringWriter();
        var originalOut = Console.Out;
        Console.SetOut(sw);

        MethodLibrary.Solve(1, 2, 1);

        var output = sw.ToString().Trim();
        Console.SetOut(originalOut);
        sw.Dispose();

        Assert.AreEqual("x = -1", output);
    }

    [TestMethod]
    public void Test4()
    {
        var sw = new StringWriter();
        var originalOut = Console.Out;
        Console.SetOut(sw);

        MethodLibrary.Solve(1, -4, 3);

        var output = sw.ToString().Trim().Split(Environment.NewLine);
        Console.SetOut(originalOut);
        sw.Dispose();

        Assert.AreEqual("x1 = 1", output[0]);
        Assert.AreEqual("x2 = 3", output[1]);
    }
}