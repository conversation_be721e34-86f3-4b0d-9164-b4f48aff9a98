namespace MyApp.Tests;

[TestClass]
public class Ex07
{
    [TestMethod]
    public void Test1()
    {
        int result = MethodLibrary.DaysInMonth(2025, 1);
        Assert.AreEqual(31, result);
    }

    [TestMethod]
    public void Test2()
    {
        int result = MethodLibrary.DaysInMonth(2024, 4);
        Assert.AreEqual(30, result);
    }

    [TestMethod]
    public void Test3()
    {
        int result = MethodLibrary.DaysInMonth(2000, 2);
        Assert.AreEqual(29, result);
    }
    [TestMethod]
    public void Test4()
    {
        int result = MethodLibrary.DaysInMonth(1900, 2);
        Assert.AreEqual(28, result);
    }
    [TestMethod]
    public void Test5()
    {
        int result = MethodLibrary.DaysInMonth(2052, 2);
        Assert.AreEqual(29, result);
    }

    [TestMethod]
    public void Test6()
    {
        int result = MethodLibrary.DaysInMonth(2053, 2);
        Assert.AreEqual(28, result);
    }

    [TestMethod]
    public void Test7()
    {
        int result = MethodLibrary.DaysInMonth(2000, 14);
        Assert.AreEqual(0, result);
    }
}