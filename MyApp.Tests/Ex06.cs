namespace MyApp.Tests;

[TestClass]
public class Ex06
{
    [TestMethod]
    public void Test1()
    {
        double result = MethodLibrary.Average(10, 1);
        Assert.AreEqual(10, result);
    }   

    [TestMethod]
    public void Test2()
    {
        double result = MethodLibrary.Average(12, 2);
        Assert.AreEqual(6, result);
    }

    [TestMethod]
    public void Test3()
    {
        double result = MethodLibrary.Average(11, 0);
        Assert.AreEqual(0, result);
    }
}