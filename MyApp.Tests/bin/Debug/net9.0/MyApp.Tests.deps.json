{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"MyApp.Tests/1.0.0": {"dependencies": {"MSTest": "3.6.4", "Microsoft.NET.Test.Sdk": "17.12.0", "MyApp": "1.0.0", "Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions": "14.0.0.0"}, "runtime": {"MyApp.Tests.dll": {}}}, "Microsoft.ApplicationInsights/2.22.0": {"dependencies": {"System.Diagnostics.DiagnosticSource": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.ApplicationInsights.dll": {"assemblyVersion": "2.22.0.997", "fileVersion": "2.22.0.997"}}}, "Microsoft.CodeCoverage/17.12.0": {"runtime": {"lib/netcoreapp3.1/Microsoft.VisualStudio.CodeCoverage.Shim.dll": {"assemblyVersion": "********", "fileVersion": "17.1200.524.48002"}}}, "Microsoft.NET.Test.Sdk/17.12.0": {"dependencies": {"Microsoft.CodeCoverage": "17.12.0", "Microsoft.TestPlatform.TestHost": "17.12.0"}}, "Microsoft.Testing.Extensions.Telemetry/1.4.3": {"dependencies": {"Microsoft.ApplicationInsights": "2.22.0", "Microsoft.Testing.Platform": "1.4.3"}, "runtime": {"lib/net8.0/Microsoft.Testing.Extensions.Telemetry.dll": {"assemblyVersion": "*******", "fileVersion": "1.400.324.56104"}}, "resources": {"lib/net8.0/cs/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.Testing.Extensions.TrxReport.Abstractions/1.4.3": {"dependencies": {"Microsoft.Testing.Platform": "1.4.3"}, "runtime": {"lib/net8.0/Microsoft.Testing.Extensions.TrxReport.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "1.400.324.56104"}}}, "Microsoft.Testing.Extensions.VSTestBridge/1.4.3": {"dependencies": {"Microsoft.ApplicationInsights": "2.22.0", "Microsoft.TestPlatform.ObjectModel": "17.12.0", "Microsoft.Testing.Extensions.Telemetry": "1.4.3", "Microsoft.Testing.Extensions.TrxReport.Abstractions": "1.4.3", "Microsoft.Testing.Platform": "1.4.3"}, "runtime": {"lib/net8.0/Microsoft.Testing.Extensions.VSTestBridge.dll": {"assemblyVersion": "*******", "fileVersion": "1.400.324.56104"}}, "resources": {"lib/net8.0/cs/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.Testing.Platform/1.4.3": {"runtime": {"lib/net8.0/Microsoft.Testing.Platform.dll": {"assemblyVersion": "*******", "fileVersion": "1.400.324.56104"}}, "resources": {"lib/net8.0/cs/Microsoft.Testing.Platform.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.Testing.Platform.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.Testing.Platform.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.Testing.Platform.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.Testing.Platform.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.Testing.Platform.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.Testing.Platform.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.Testing.Platform.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.Testing.Platform.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.Testing.Platform.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.Testing.Platform.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.Testing.Platform.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.Testing.Platform.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.Testing.Platform.MSBuild/1.4.3": {"dependencies": {"Microsoft.Testing.Platform": "1.4.3"}, "runtime": {"lib/net8.0/Microsoft.Testing.Platform.MSBuild.dll": {"assemblyVersion": "*******", "fileVersion": "1.400.324.56104"}}, "resources": {"lib/net8.0/cs/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.Testing.Platform.MSBuild.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.TestPlatform.ObjectModel/17.12.0": {"dependencies": {"System.Reflection.Metadata": "1.6.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.TestPlatform.CoreUtilities.dll": {"assemblyVersion": "********", "fileVersion": "17.1200.24.56501"}, "lib/netcoreapp3.1/Microsoft.TestPlatform.PlatformAbstractions.dll": {"assemblyVersion": "********", "fileVersion": "17.1200.24.56501"}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll": {"assemblyVersion": "********", "fileVersion": "17.1200.24.56501"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.TestPlatform.TestHost/17.12.0": {"dependencies": {"Microsoft.TestPlatform.ObjectModel": "17.12.0", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netcoreapp3.1/Microsoft.TestPlatform.CommunicationUtilities.dll": {"assemblyVersion": "********", "fileVersion": "17.1200.24.56501"}, "lib/netcoreapp3.1/Microsoft.TestPlatform.CrossPlatEngine.dll": {"assemblyVersion": "********", "fileVersion": "17.1200.24.56501"}, "lib/netcoreapp3.1/Microsoft.TestPlatform.Utilities.dll": {"assemblyVersion": "********", "fileVersion": "17.1200.24.56501"}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.Common.dll": {"assemblyVersion": "********", "fileVersion": "17.1200.24.56501"}, "lib/netcoreapp3.1/testhost.dll": {"assemblyVersion": "********", "fileVersion": "17.1200.24.56501"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/cs/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/de/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/es/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/fr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/it/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ja/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/ko/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pl/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/pt-BR/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/ru/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/tr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "MSTest/3.6.4": {"dependencies": {"MSTest.Analyzers": "3.6.4", "MSTest.TestAdapter": "3.6.4", "MSTest.TestFramework": "3.6.4", "Microsoft.NET.Test.Sdk": "17.12.0"}}, "MSTest.Analyzers/3.6.4": {}, "MSTest.TestAdapter/3.6.4": {"dependencies": {"Microsoft.Testing.Extensions.VSTestBridge": "1.4.3", "Microsoft.Testing.Platform.MSBuild": "1.4.3"}}, "MSTest.TestFramework/3.6.4": {"runtime": {"lib/net8.0/Microsoft.VisualStudio.TestPlatform.TestFramework.dll": {"assemblyVersion": "14.0.0.0", "fileVersion": "3.600.424.60307"}}, "resources": {"lib/net8.0/cs/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.VisualStudio.TestPlatform.TestFramework.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Newtonsoft.Json/13.0.1": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.1.25517"}}}, "System.Diagnostics.DiagnosticSource/5.0.0": {}, "System.Reflection.Metadata/1.6.0": {}, "MyApp/1.0.0": {"runtime": {"MyApp.dll": {"assemblyVersion": "1.0.0", "fileVersion": "1.0.0.0"}}}, "Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions/14.0.0.0": {"runtime": {"Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions.dll": {"assemblyVersion": "14.0.0.0", "fileVersion": "3.600.424.60307"}}}}}, "libraries": {"MyApp.Tests/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.ApplicationInsights/2.22.0": {"type": "package", "serviceable": true, "sha512": "sha512-3AOM9bZtku7RQwHyMEY3tQMrHIgjcfRDa6YQpd/QG2LDGvMydSlL9Di+8LLMt7J2RDdfJ7/2jdYv6yHcMJAnNw==", "path": "microsoft.applicationinsights/2.22.0", "hashPath": "microsoft.applicationinsights.2.22.0.nupkg.sha512"}, "Microsoft.CodeCoverage/17.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-4svMznBd5JM21JIG2xZKGNanAHNXplxf/kQDFfLHXQ3OnpJkayRK/TjacFjA+EYmoyuNXHo/sOETEfcYtAzIrA==", "path": "microsoft.codecoverage/17.12.0", "hashPath": "microsoft.codecoverage.17.12.0.nupkg.sha512"}, "Microsoft.NET.Test.Sdk/17.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-kt/PKBZ91rFCWxVIJZSgVLk+YR+4KxTuHf799ho8WNiK5ZQpJNAEZCAWX86vcKrs+DiYjiibpYKdGZP6+/N17w==", "path": "microsoft.net.test.sdk/17.12.0", "hashPath": "microsoft.net.test.sdk.17.12.0.nupkg.sha512"}, "Microsoft.Testing.Extensions.Telemetry/1.4.3": {"type": "package", "serviceable": true, "sha512": "sha512-dh8jnqWikxQXJ4kWy8B82PtSAlQCnvDKh1128arDmSW5OU5xWA84HwruV3TanXi3ZjIHn1wWFCgtMOhcDNwBow==", "path": "microsoft.testing.extensions.telemetry/1.4.3", "hashPath": "microsoft.testing.extensions.telemetry.1.4.3.nupkg.sha512"}, "Microsoft.Testing.Extensions.TrxReport.Abstractions/1.4.3": {"type": "package", "serviceable": true, "sha512": "sha512-16sWznD6ZMok/zgW+vrO6zerCFMD9N+ey9bi1iV/e9xxsQb4V4y/aW6cY/Y7E9jA7pc+aZ6ffZby43yxQOoYZA==", "path": "microsoft.testing.extensions.trxreport.abstractions/1.4.3", "hashPath": "microsoft.testing.extensions.trxreport.abstractions.1.4.3.nupkg.sha512"}, "Microsoft.Testing.Extensions.VSTestBridge/1.4.3": {"type": "package", "serviceable": true, "sha512": "sha512-xZ6oyNYh2aM5Wb+HJAy1fj2C4CNRVhINXHCjlWs/2C8hEIpdqVSpP3y6HWUN40KpFqyGD4myHGR1Rflm28UpcQ==", "path": "microsoft.testing.extensions.vstestbridge/1.4.3", "hashPath": "microsoft.testing.extensions.vstestbridge.1.4.3.nupkg.sha512"}, "Microsoft.Testing.Platform/1.4.3": {"type": "package", "serviceable": true, "sha512": "sha512-Ned<PERSON>bwl1T7+ZMeg7gwk0Db8/RFLf0siyVpeTcRMMOle6Xl/ujaYOM4Aduo8rEfVqNj3kcQ7blegpyT3dHi+0PA==", "path": "microsoft.testing.platform/1.4.3", "hashPath": "microsoft.testing.platform.1.4.3.nupkg.sha512"}, "Microsoft.Testing.Platform.MSBuild/1.4.3": {"type": "package", "serviceable": true, "sha512": "sha512-1gGqgHtiZ6tZn/6Tby+qlKpNe5Ye/5LnxlSsyl4XMZ4m4V+Cu1K1m+gD1zxoxHIvLjgX8mCnQRK95MGBBFuumw==", "path": "microsoft.testing.platform.msbuild/1.4.3", "hashPath": "microsoft.testing.platform.msbuild.1.4.3.nupkg.sha512"}, "Microsoft.TestPlatform.ObjectModel/17.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-TDqkTKLfQuAaPcEb3pDDWnh7b3SyZF+/W9OZvWFp6eJCIiiYFdSB6taE2I6tWrFw5ywhzOb6sreoGJTI6m3rSQ==", "path": "microsoft.testplatform.objectmodel/17.12.0", "hashPath": "microsoft.testplatform.objectmodel.17.12.0.nupkg.sha512"}, "Microsoft.TestPlatform.TestHost/17.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-MiPEJQNyADfwZ4pJNpQex+t9/jOClBGMiCiVVFuELCMSX2nmNfvUor3uFVxNNCg30uxDP8JDYfPnMXQzsfzYyg==", "path": "microsoft.testplatform.testhost/17.12.0", "hashPath": "microsoft.testplatform.testhost.17.12.0.nupkg.sha512"}, "MSTest/3.6.4": {"type": "package", "serviceable": true, "sha512": "sha512-PDBqb7FT15DBD/LQtAr2Eq/UY9YVTgsY7CD7ZiDnamc/RI+/2VSak6qotTV+x2oyhcRJxE4USRgyqXIRlyU3kw==", "path": "mstest/3.6.4", "hashPath": "mstest.3.6.4.nupkg.sha512"}, "MSTest.Analyzers/3.6.4": {"type": "package", "serviceable": true, "sha512": "sha512-4gU/VdItLebmE2+UkOaqffVmVa/in0VeIF9fmN/fG0tj5AHAasjasJcZa9U2uXBNX03cKCWlgWenlhKLz343NQ==", "path": "mstest.analyzers/3.6.4", "hashPath": "mstest.analyzers.3.6.4.nupkg.sha512"}, "MSTest.TestAdapter/3.6.4": {"type": "package", "serviceable": true, "sha512": "sha512-YdwseRA+nDhRqD2oPHjCE4KzLEN5B10A61lOslE3N3OvUwHJ6ezyZZjYWf7mrZ8jckCcx/UlBclTzgWUpMpPQw==", "path": "mstest.testadapter/3.6.4", "hashPath": "mstest.testadapter.3.6.4.nupkg.sha512"}, "MSTest.TestFramework/3.6.4": {"type": "package", "serviceable": true, "sha512": "sha512-3nV+2CJluKmiJpCSqQfXu5idCq35+vqFywjScyauTIz0Zk7KJw7Qpzv8gtwow0To7pxIlIvwkq9rbMB+V6eOow==", "path": "mstest.testframework/3.6.4", "hashPath": "mstest.testframework.3.6.4.nupkg.sha512"}, "Newtonsoft.Json/13.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "path": "newtonsoft.json/13.0.1", "hashPath": "newtonsoft.json.13.0.1.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tCQTzPsGZh/A9LhhA6zrqCRV4hOHsK90/G7q3Khxmn6tnB1PuNU0cRaKANP2AWcF9bn0zsuOoZOSrHuJk6oNBA==", "path": "system.diagnostics.diagnosticsource/5.0.0", "hashPath": "system.diagnostics.diagnosticsource.5.0.0.nupkg.sha512"}, "System.Reflection.Metadata/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-COC1aiAJjCoA5GBF+QKL2uLqEBew4JsCkQmoHKbN3TlOZKa2fKLz5CpiRQKDz0RsAOEGsVKqOD5bomsXq/4STQ==", "path": "system.reflection.metadata/1.6.0", "hashPath": "system.reflection.metadata.1.6.0.nupkg.sha512"}, "MyApp/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.VisualStudio.TestPlatform.TestFramework.Extensions/14.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}}}