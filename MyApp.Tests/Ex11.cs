using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Globalization;
using System.IO;

namespace MyApp.Tests;

    [TestClass]
    public class Ex11
    {
        private const string CsvPath = "data/Ex11.csv";

        [TestMethod]
        public void Test()
        {
        using var reader = new StreamReader(CsvPath);
        string header = reader.ReadLine();

        while (!reader.EndOfStream)
        {
            var line = reader.ReadLine();
            var parts = line.Split(',');

            float w = float.Parse(parts[0], CultureInfo.InvariantCulture);
            float h = float.Parse(parts[1], CultureInfo.InvariantCulture);
            float ww = float.Parse(parts[2], CultureInfo.InvariantCulture);
            float wh = float.Parse(parts[3], CultureInfo.InvariantCulture);
            string expected = parts[4].Trim();

            if (expected.StartsWith("Exception"))
            {
                Assert.ThrowsException<Exception>(() =>
                {
                    MethodLibrary.ComputePosition(w, h, ww, wh);
                }, $"Expected exception for line: {line}");
            }
            else
            {
                var result = MethodLibrary.ComputePosition(w, h, ww, wh);
                string resultString = $"({result.x},{result.y})";

                Assert.AreEqual(expected, resultString,
                    $"Input: {w},{h},{ww},{wh} — Expected: {expected}, Got: {resultString}");
            }
        }
    }
    }