namespace MyApp.Tests;

[TestClass]
public class Ex09
{
    [TestMethod]
    public void Test1()
    {
        string result = MethodLibrary.Ex9(9, 1, 11);
        Assert.AreEqual("error", result);
    }

    [TestMethod]
    public void Test2()
    {
        string result = MethodLibrary.Ex9(5, 9, 1);
        Assert.AreEqual("5", result);
    }

    [TestMethod]
    public void Test3()
    {
        string result = MethodLibrary.Ex9(4, 6, 9);
        Assert.AreEqual("6", result);
    }

    [TestMethod]
    public void Test4()
    {
        string result = MethodLibrary.Ex9(2, 9, 4);
        Assert.AreEqual("4", result);
    }
}