namespace MyApp.Tests;

[TestClass]
public class Ex09
{
    [TestMethod]
    public void Test1()
    {
        string result = MethodLibrary.Ex9(5, 3, 4);
        Assert.AreEqual("error", result);
    }

    [TestMethod]
    public void Test2()
    {
        string result = MethodLibrary.Ex9(1, 5, 0);
        Assert.AreEqual("1", result);
    }

    [TestMethod]
    public void Test3()
    {
        string result = MethodLibrary.Ex9(1, 5, 10);
        Assert.AreEqual("5", result);
    }

    [TestMethod]
    public void Test4()
    {
        string result = MethodLibrary.Ex9(1, 5, 3);
        Assert.AreEqual("3", result);
    }
}