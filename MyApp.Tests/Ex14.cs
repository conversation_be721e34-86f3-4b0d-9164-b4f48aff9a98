using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Globalization;
using System.IO;

namespace MyApp.Tests;

[TestClass]
public class Ex14
{
    private const string CsvPath = "data/Ex14.csv";

    [TestMethod]
    public void Test()
    {
        using var reader = new StreamReader(CsvPath);
        string? header = reader.ReadLine();

        while (!reader.EndOfStream)
        {
            string? line = reader.ReadLine();
            if (line == null) continue;

            var parts = line.Split(',', 5);
            if (parts.Length < 5) continue;

            float w = float.Parse(parts[0], CultureInfo.InvariantCulture);
            float h = float.Parse(parts[1], CultureInfo.InvariantCulture);
            string expected = parts[4].Trim();

            if (expected.StartsWith("Exception"))
            {
                Assert.ThrowsException<Exception>(() =>
                {
                    MethodLibrary.CropSquare(w, h);
                }, $"Expected exception for line: {line}");
            }
            else
            {
                var result = MethodLibrary.CropSquare(w, h);
                string resultString = $"({result.x},{result.y},{result.s})";

                Assert.AreEqual(expected, resultString,
                    $"Input: {w},{h} — Expected: {expected}, Got: {resultString}");
            }
        }
    }
}