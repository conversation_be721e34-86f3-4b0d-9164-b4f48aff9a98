// namespace MyApp.Tests;

// [TestClass]
// public class Ex04
// {
//     [TestMethod]
//     public void Test1()
//     {
//         int result = MethodLibrary.Max(10, 14, 16);
//         Assert.AreEqual(16, result);
//     }

//     [TestMethod]
//     public void Test2()
//     {
//         int result = MethodLibrary.Max(12, 13, 11);
//         Assert.AreEqual(13, result);
//     }

//     [TestMethod]
//     public void Test3()
//     {
//         int result = MethodLibrary.Max(15, 12, 17);
//         Assert.AreEqual(17, result);
//     }

//     [TestMethod]
//     public void Test4()
//     {
//         int result = MethodLibrary.Max(14, 13, 11);
//         Assert.AreEqual(14, result);
//     }

//     [TestMethod]
//     public void Test5()
//     {
//         int result = MethodLibrary.Max(0, 11, 12);
//         Assert.AreEqual(12, result);
//     }
// }