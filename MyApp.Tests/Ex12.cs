using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Globalization;
using System.IO;

namespace MyApp.Tests;

[TestClass]
public class Ex12
{
    private const string CsvPath = "data/Ex12.csv";

    [TestMethod]
    public void Test()
    {
        using var reader = new StreamReader(CsvPath);
        string? header = reader.ReadLine();

        while (!reader.EndOfStream)
        {
            string? line = reader.ReadLine();
            if (line == null) continue;

            var parts = line.Split(',');

            float total = float.Parse(parts[0], CultureInfo.InvariantCulture);
            float p1 = float.Parse(parts[1], CultureInfo.InvariantCulture);
            float p2 = float.Parse(parts[2], CultureInfo.InvariantCulture);
            float p3 = float.Parse(parts[3], CultureInfo.InvariantCulture);
            string expected = parts[4].Trim();

            if (expected.StartsWith("Exception"))
            {
                Assert.ThrowsException<Exception>(() =>
                {
                    MethodLibrary.CalculateTotalCost(total, p1, p2, p3);
                }, $"Expected exception for line: {line}");
            }
            else
            {
                float expectedValue = float.Parse(expected, CultureInfo.InvariantCulture);
                float result = MethodLibrary.CalculateTotalCost(total, p1, p2, p3);

                Assert.AreEqual(expectedValue, result,
                    $"Input: total={total}, p1={p1}, p2={p2}, p3={p3} — Expected: {expectedValue}, Got: {result}");
            }
        }
    }
}