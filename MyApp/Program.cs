namespace MyApp;

public class MethodLibrary
{
    public static int GetPositionOf2Cir(int firstRadius, int secRadius, int distance)
    {
        if (distance == 0)
        {
            if (firstRadius == secRadius)
            {
                return 0;
            }
            else if (firstRadius < secRadius)
            {
                return 1;
            }
            else
            {
                return 2;
            }
        }

        if (distance > 0)
        {
            return 3;
        }

        return -1;
    }

    public static string GetResult(int X, int Y, int Z)
    {
        if (X == 1 || X == 2)
        {
            return "A";
        }
        else
        {
            if (Y <= 10)
            {
                return "B";
            }
            else
            {
                if (Z < 5)
                {
                    return "C";
                }
                else
                {
                    return "D";
                }
            }
        }
    }

    public int MaxAndMean(int A, int B, int C, out double Mean)
    {
        Mean = (A + B + C) / 3.0;

        int Maximum;

        if (A > B)
        {
            if (A > C)
                Maximum = A;
            else
                Maximum = B;
        }
        else if (B > C)
        {
            Maximum = B;
        }
        else
        {
            Maximum = C;
        }

        return Maximum;
    }

    public static int Max(int a, int b, int c)
    {
        int max = 0;

        if (a > 0 && b > 0 && c > 0)
        {
            max = a;
        }
        else
        {
            return 0;
        }
        if (max < b)
            max = b;

        if (max < c)
            max = c;

        return max;
    }

    

}

class Program
{
    static void Main(string[] args)
    {
        Console.WriteLine("Hello from MyApp!");
    }
}
