namespace MyApp;

public class MethodLibrary
{
    public static int GetPositionOf2Cir(int firstRadius, int secRadius, int distance)
    {
        if (distance == 0)
        {
            if (firstRadius == secRadius)
            {
                return 0;
            }
            else if (firstRadius < secRadius)
            {
                return 1;
            }
            else
            {
                return 2;
            }
        }

        if (distance > 0)
        {
            return 3;
        }
        return -1;
    }

    public static string GetResult(int X, int Y, int Z)
    {
        if (X == 1 || X == 2)
        {
            return "A";
        }
        else
        {
            if (Y <= 10)
            {
                return "B";
            }
            else
            {
                if (Z < 5)
                {
                    return "C";
                }
                else
                {
                    return "D";
                }
            }
        }
    }

    public static int MaxAndMean(int A, int B, int C, out double Mean)
    {
        Mean = (A + B + C) / 3.0;

        int Maximum;

        if (A > B)
        {
            if (A > C)
                Maximum = A;
            else
                Maximum = B;
        }
        else if (B > C)
        {
            Maximum = B;
        }
        else
        {
            Maximum = C;
        }

        return Maximum;
    }

    public static int Max(int a, int b, int c)
    {
        int max = 0;

        if (a > 0 && b > 0 && c > 0)
        {
            max = a;
        }
        else
        {
            return 0;
        }
        if (max < b)
            max = b;

        if (max < c)
            max = c;

        return max;
    }

    public static double Average(double sum, double count)
    {
        if (count == 1) return sum;
        else if (count > 0) return sum / count;
        else return 0;
    }

    public static int DaysInMonth(ushort year, byte month)
    {
        switch (month)
        {
            case 1:
            case 3:
            case 5:
            case 7:
            case 8:
            case 10:
            case 12:
                return 31;

            case 4:
            case 6:
            case 9:
            case 11:
                return 30;

            case 2:
                if (year % 400 == 0)
                {
                    return 29;
                }
                else if (year % 100 == 0)
                {
                    return 28;
                }
                else if (year % 4 == 0)
                {
                    return 29;
                }
                else
                {
                    return 28;
                }

            default:
                return 0;
        }
    }

    public static void Solve(float a, float b, float c)
    {
        float delta;

        if (a == 0)
        {
            Console.WriteLine("error");
            return;
        }
        else
        {
            delta = b * b - 4 * a * c;

            if (delta < 0)
            {
                Console.WriteLine("no results");
            }
            else if (delta == 0)
            {
                Console.WriteLine("x = " + (-b / (2 * a)));
            }
            else
            {
                Console.WriteLine("x1 = " + ((-b - Math.Sqrt(delta)) / (2 * a)));
                Console.WriteLine("x2 = " + ((-b + Math.Sqrt(delta)) / (2 * a)));
            }
        }
    }

    public static string Ex9(float a, float b, float x)
    {
        if (a > b)
        {
            return "error";
        }
        else
        {
            if (x < a)
            {
                return a.ToString();
            }
            else
            {
                if (x > b)
                {
                    return b.ToString();
                }
                else
                {
                    return x.ToString();
                }
            }
        }
    }

    public static bool IsTriangle(float a, float b, float c)
    {
        if (a <= 0 || b <= 0 || c <= 0)
        {
            return false;
        }
        else
        {
            if (a + b <= c)
            {
                return false;
            }
            else
            {
                if (a + c <= b)
                {
                    return false;
                }
                else
                {
                    if (b + c <= a)
                    {
                        return false;
                    }
                    else
                    {
                        return true;
                    }
                }
            }
        }
    }

    public static (float x, float y) ComputePosition(float w, float h, float ww, float wh)
    {
        if (w <= 0 || h <= 0 || ww <= 0 || wh <= 0)
            throw new Exception("error");

        float x, y;

        // Tính x
        if (w > ww)
            x = 0;
        else
            x = (ww - w) / 2;

        // Tính y
        if (h > wh)
            y = 0;
        else
            y = (wh - h) / 2;

        return (x, y);
    }

}

class Program
{
    static void Main(string[] args)
    {
        Console.WriteLine("Hello from MyApp!");
    }
}
